<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>
        <%= pageTitle %>
    </title>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Work+Sans%3Awght%40400%3B500%3B700%3B900" />
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/pages/login/login.css">
</head>

<body>
    <div class="login-page">
        <div class="login-layout-container">
            <header class="login-header">
                <div class="login-header-content">
                    <h2 class="login-logo-title">InfraWatch</h2>
                </div>
            </header>
            <div class="login-content-wrapper">
                <div class="login-content-container">
                    <h2 class="login-title">Login</h2>
                    <form id="loginForm" class="login-form">
                        <div class="login-field-group">
                            <label class="login-field-label">
                                <p class="login-label-text">Email</p>
                                <input placeholder="<EMAIL>" type="email" id="email" name="email"
                                    class="login-input" value="" />
                            </label>
                        </div>
                        <div class="login-field-group">
                            <label class="login-field-label">
                                <p class="login-label-text">Senha</p>
                                <input placeholder="Senha" type="password" id="senha" name="senha" class="login-input"
                                    value="" />
                            </label>
                        </div>
                        <div class="login-button-group">
                            <button type="submit" class="login-submit-button">
                                <span class="login-button-text">Entrar</span>
                            </button>
                        </div>
                        <div id="errorMessage" class="login-error-message"></div>
                        <div class="login-help-section">
                            <a href="/recuperar-senha" class="login-help-link">Problemas para entrar?</a>
                        </div>
                        <div class="login-signup-section">
                            <p class="login-signup-text">Não tem uma conta? <a href="/cadastro"
                                    class="login-signup-link">Crie seu Cadastro</a></p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', function (event) {
                    event.preventDefault();
                    const emailInput = document.getElementById('email');
                    const passwordInput = document.getElementById('senha');
                    const errorMessageElement = document.getElementById('errorMessage');

                    const email = emailInput.value.trim();
                    const password = passwordInput.value.trim();

                    errorMessageElement.textContent = '';

                    if (email === '<EMAIL>' && password === '123456') {
                        window.location.href = '/admin/dashboard';
                    } else if (email === '<EMAIL>' && password === '123456') {
                        window.location.href = '/inspetor/lista-inspecoes';
                    } else {
                        errorMessageElement.textContent = 'Email ou senha inválidos.';
                    }
                });
            }
        });
    </script>
</body>

</html>