<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meu Perfil - InfraWatch</title>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/pages/inspetor/perfil.css">
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="icon" type="image/x-icon" href="../ícones/disruption.png" />
</head>

<body x-data="{
    isEditing: false,
    user: {
        name: 'Rafael Inspetor',
        email: '<EMAIL>',
        specialization: 'Engenheiro Civil',
        phone: '(11) 98765-4321',
        avatar: '../ícones/perfil.png'
    },
    tempUser: {},
    startEditing() {
        this.tempUser = JSON.parse(JSON.stringify(this.user));
        this.isEditing = true;
    },
    saveChanges() {
        this.user = JSON.parse(JSON.stringify(this.tempUser));
        this.isEditing = false;
        alert('Perfil atualizado com sucesso!');
    },
    cancelEditing() {
        this.isEditing = false;
    },
    handleAvatarChange(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                this.tempUser.avatar = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    }
}">
    <div class="perfil-inspetor-page">
        <div class="perfil-inspetor-layout-container">
            <header class="perfil-inspetor-header">
                <div class="perfil-inspetor-logo-section">
                    <a href="/inspetor/lista-inspecoes" class="perfil-inspetor-logo-title">InfraWatch</a>
                </div>
                <nav class="perfil-inspetor-nav">
                    <div class="perfil-inspetor-nav-list">
                        <a class="perfil-inspetor-nav-item" href="/inspetor/lista-inspecoes">Inspeções</a>
                        <a class="perfil-inspetor-nav-item" href="/inspetor/relatorios">Relatórios</a>
                    </div>
                </nav>

                <div class="perfil-inspetor-user-section">
                    <div class="perfil-inspetor-notifications-container"
                        x-data="{ notificationsOpen: false, hasNotifications: true, toggleNotifications() { this.notificationsOpen = !this.notificationsOpen; }, closeNotifications() { this.notificationsOpen = false; } }">
                        <button @click="toggleNotifications()" class="perfil-inspetor-notification-button">
                            <svg class="perfil-inspetor-notification-icon" xmlns="http://www.w3.org/2000/svg"
                                fill="currentColor" viewBox="0 0 256 256">
                                <path
                                    d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-34.92,16-80a64,64,0,1,1,128,0c0,45.08,8.3,66.76,16,80Z">
                                </path>
                            </svg>
                            <div x-show="hasNotifications" class="perfil-inspetor-notification-badge"></div>
                        </button>
                        <div x-show="notificationsOpen" @click.away="closeNotifications()"
                            class="perfil-inspetor-notification-dropdown" x-cloak>
                            <div class="perfil-inspetor-notification-header">Notificações</div>
                            <div class="perfil-inspetor-notification-divider"></div>
                            <div class="perfil-inspetor-notification-list">
                                <a href="#" class="perfil-inspetor-notification-item">
                                    Nova inspeção atribuída.
                                </a>
                                <a href="#" class="perfil-inspetor-notification-item">
                                    Prazo de inspeção se aproximando.
                                </a>
                            </div>
                        </div>
                    </div>
                    <a href="/inspetor/perfil">
                        <div class="perfil-inspetor-user-avatar" :style="`background-image: url('${user.avatar}');`">
                        </div>
                    </a>
                </div>
            </header>

            <div class="perfil-inspetor-content-wrapper">
                <div class="perfil-inspetor-content-container">
                    <div class="perfil-inspetor-header-section">
                        <h1 class="perfil-inspetor-title">Meu Perfil</h1>
                        <button x-show="!isEditing" @click="startEditing()" class="perfil-inspetor-edit-btn">
                            <img src="/images/settings.png" alt="Editar Perfil" width="20px" height="20px"
                                style="filter: brightness(0) invert(1);">
                            Editar Perfil
                        </button>
                    </div>

                    <div class="perfil-inspetor-profile-section">
                        <div class="perfil-inspetor-avatar-section">
                            <div class="perfil-inspetor-avatar-container">
                                <img :src="isEditing ? tempUser.avatar : user.avatar" alt="Foto do Perfil"
                                    class="perfil-inspetor-avatar-image">
                                <label x-show="isEditing" for="avatarUpload" class="perfil-inspetor-avatar-upload">
                                    <input type="file" id="avatarUpload" @change="handleAvatarChange" accept="image/*">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                        class="bi bi-camera-fill" viewBox="0 0 16 16">
                                        <path d="M10.5 8.5a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0z" />
                                        <path
                                            d="M2 4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2h-1.172a2 2 0 0 1-1.414-.586l-.828-.828A2 2 0 0 0 9.172 2H6.828a2 2 0 0 0-1.414.586l-.828.828A2 2 0 0 1 3.172 4H2zm.5 2a.5.5 0 1 1 0-1 .5.5 0 0 1 0 1zm9 2.5a3.5 3.5 0 1 1-7 0 3.5 3.5 0 0 1 7 0z" />
                                    </svg>
                                </label>
                            </div>
                            <h2 class="perfil-inspetor-user-name" x-text="user.name"></h2>
                            <p class="perfil-inspetor-user-specialization" x-text="user.specialization"></p>
                        </div>

                        <div class="perfil-inspetor-info-section">
                            <dl class="perfil-inspetor-info-grid">
                                <div class="perfil-inspetor-field-group">
                                    <dt class="perfil-inspetor-field-label">Nome Completo</dt>
                                    <dd>
                                        <span x-show="!isEditing" class="perfil-inspetor-field-value"
                                            x-text="user.name"></span>
                                        <input x-show="isEditing" type="text" x-model="tempUser.name"
                                            class="perfil-inspetor-field-input">
                                    </dd>
                                </div>
                                <div class="perfil-inspetor-field-group">
                                    <dt class="perfil-inspetor-field-label">Endereço de Email</dt>
                                    <dd>
                                        <span x-show="!isEditing" class="perfil-inspetor-field-value"
                                            x-text="user.email"></span>
                                        <input x-show="isEditing" type="email" x-model="tempUser.email"
                                            class="perfil-inspetor-field-input">
                                    </dd>
                                </div>
                                <div class="perfil-inspetor-field-group">
                                    <dt class="perfil-inspetor-field-label">Telefone</dt>
                                    <dd>
                                        <span x-show="!isEditing" class="perfil-inspetor-field-value"
                                            x-text="user.phone"></span>
                                        <input x-show="isEditing" type="tel" x-model="tempUser.phone"
                                            class="perfil-inspetor-field-input">
                                    </dd>
                                </div>
                                <div class="perfil-inspetor-field-group">
                                    <dt class="perfil-inspetor-field-label">Especialização</dt>
                                    <dd>
                                        <span x-show="!isEditing" class="perfil-inspetor-field-value"
                                            x-text="user.specialization"></span>
                                        <input x-show="isEditing" type="text" x-model="tempUser.specialization"
                                            class="perfil-inspetor-field-input">
                                    </dd>
                                </div>
                            </dl>
                        </div>

                        <div x-show="isEditing" class="perfil-inspetor-actions">
                            <button @click="cancelEditing()" class="perfil-inspetor-cancel-btn">
                                Cancelar
                            </button>
                            <button @click="saveChanges()" class="perfil-inspetor-save-btn">
                                Salvar Alterações
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>