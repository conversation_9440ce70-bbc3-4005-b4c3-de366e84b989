<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inspeções - InfraWatch</title>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/pages/inspetor/lista-inspecoes.css">
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="icon" type="image/x-icon" href="../ícones/disruption.png" />
</head>

<body>
    <div class="lista-inspecoes-inspetor-page">
        <div class="lista-inspecoes-inspetor-layout-container">
            <header class="lista-inspecoes-inspetor-header">
                <div class="lista-inspecoes-inspetor-logo-section">
                    <a href="/inspetor/lista-inspecoes" class="lista-inspecoes-inspetor-logo-title">InfraWatch</a>
                </div>
                <nav class="lista-inspecoes-inspetor-nav">
                    <div class="lista-inspecoes-inspetor-nav-list">
                        <a class="lista-inspecoes-inspetor-nav-item active"
                            href="/inspetor/lista-inspecoes">Inspeções</a>
                        <a class="lista-inspecoes-inspetor-nav-item" href="/inspetor/relatorios">Relatórios</a>
                    </div>
                </nav>

                <div class="lista-inspecoes-inspetor-user-section">
                    <div class="lista-inspecoes-inspetor-notifications-container"
                        x-data="{ notificationsOpen: false, hasNotifications: true, toggleNotifications() { this.notificationsOpen = !this.notificationsOpen; }, closeNotifications() { this.notificationsOpen = false; } }">
                        <button @click="toggleNotifications()" class="lista-inspecoes-inspetor-notification-button">
                            <svg class="lista-inspecoes-inspetor-notification-icon" xmlns="http://www.w3.org/2000/svg"
                                fill="currentColor" viewBox="0 0 256 256">
                                <path
                                    d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-34.92,16-80a64,64,0,1,1,128,0c0,45.08,8.3,66.76,16,80Z">
                                </path>
                            </svg>
                            <div x-show="hasNotifications" class="lista-inspecoes-inspetor-notification-badge"></div>
                        </button>
                        <div x-show="notificationsOpen" @click.away="closeNotifications()"
                            class="lista-inspecoes-inspetor-notification-dropdown" x-cloak>
                            <div class="lista-inspecoes-inspetor-notification-header">Notificações</div>
                            <div class="lista-inspecoes-inspetor-notification-divider"></div>
                            <div class="lista-inspecoes-inspetor-notification-list">
                                <a href="#" class="lista-inspecoes-inspetor-notification-item">
                                    Nova inspeção atribuída.
                                </a>
                                <a href="#" class="lista-inspecoes-inspetor-notification-item">
                                    Prazo de inspeção se aproximando.
                                </a>
                            </div>
                        </div>
                    </div>
                    <a href="/inspetor/perfil" class="lista-inspecoes-inspetor-user-avatar"
                        style="background-image: url('/images/perfil.png');">
                    </a>
                </div>
            </header>

            <div class="lista-inspecoes-inspetor-content-wrapper">
                <div class="lista-inspecoes-inspetor-content-container">
                    <div class="lista-inspecoes-inspetor-header-section">
                        <h1 class="lista-inspecoes-inspetor-title">Minhas Inspeções</h1>
                    </div>

                    <div class="lista-inspecoes-inspetor-search-section">
                        <label class="lista-inspecoes-inspetor-search-container">
                            <div class="lista-inspecoes-inspetor-search-wrapper">
                                <div class="lista-inspecoes-inspetor-search-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px"
                                        fill="currentColor" viewBox="0 0 256 256">
                                        <path
                                            d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z">
                                        </path>
                                    </svg>
                                </div>
                                <input placeholder="Buscar inspeções..." class="lista-inspecoes-inspetor-search-input"
                                    value="" />
                            </div>
                        </label>
                    </div>

                    <div class="lista-inspecoes-inspetor-filters">
                        <div class="lista-inspecoes-inspetor-filter-group">
                            <label for="statusFilter" class="lista-inspecoes-inspetor-filter-label">Filtrar por
                                Status</label>
                            <select id="statusFilter" name="statusFilter"
                                class="lista-inspecoes-inspetor-filter-select">
                                <option value="">Todos</option>
                                <option value="Agendada">Agendada</option>
                                <option value="Em Andamento">Em Andamento</option>
                                <option value="Concluída">Concluída</option>
                            </select>
                        </div>
                        <div class="lista-inspecoes-inspetor-filter-group">
                            <label for="startDateFilter" class="lista-inspecoes-inspetor-filter-label">Data de
                                Início</label>
                            <input type="date" id="startDateFilter" name="startDateFilter"
                                class="lista-inspecoes-inspetor-filter-input">
                        </div>
                        <div class="lista-inspecoes-inspetor-filter-group">
                            <label for="endDateFilter" class="lista-inspecoes-inspetor-filter-label">Data de
                                Término</label>
                            <input type="date" id="endDateFilter" name="endDateFilter"
                                class="lista-inspecoes-inspetor-filter-input">
                        </div>
                    </div>

                    <div class="lista-inspecoes-inspetor-table-section">
                        <div class="lista-inspecoes-inspetor-table-container">
                            <table class="lista-inspecoes-inspetor-table">
                                <thead>
                                    <tr>
                                        <th style="width: 150px;">ID da Inspeção</th>
                                        <th style="width: 250px;">Endereço</th>
                                        <th style="width: 150px;">Status</th>
                                        <th style="width: 150px;">Data de Início</th>
                                        <th style="width: 150px;">Data de Término</th>
                                        <th style="width: 150px;">Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Exemplo de Linha de Inspeção (substituir com dados dinâmicos) -->
                                    <tr class="border-t border-t-[#dce1e5]">
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            INSP-001</td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[250px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            Rua das Palmeiras, 123, Cidade Exemplo</td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center">
                                            <span
                                                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[#fffbe6] text-[#faad14]">Em
                                                Andamento</span>
                                        </td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            25/05/2025</td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            28/05/2025</td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center">
                                            <a href="detalhesInspecao.html?id=INSP-001"
                                                class="text-blue-600 hover:underline mr-2">Ver Detalhes</a>
                                            <a href="iniciarInspecao.html?id=INSP-001"
                                                class="text-green-600 hover:underline">Continuar</a>
                                        </td>
                                    </tr>
                                    <tr class="border-t border-t-[#dce1e5]">
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            INSP-002</td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[250px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            Av. dos Bosques, 456, Vila Verde</td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center">
                                            <span
                                                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[#e6f7ff] text-[#007bff]">Agendada</span>
                                        </td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            10/06/2025</td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            12/06/2025</td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center">
                                            <a href="detalhesInspecao.html?id=INSP-002"
                                                class="text-blue-600 hover:underline mr-2">Ver Detalhes</a>
                                            <a href="iniciarInspecao.html?id=INSP-002"
                                                class="text-green-600 hover:underline">Iniciar</a>
                                        </td>
                                    </tr>
                                    <tr class="border-t border-t-[#dce1e5]">
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            INSP-003</td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[250px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            Praça da Matriz, 789</td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center">
                                            <span
                                                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[#f6ffed] text-[#52c41a]">Concluída</span>
                                        </td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            15/05/2025</td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            17/05/2025</td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center">
                                            <a href="detalhesInspecao.html?id=INSP-003"
                                                class="text-blue-600 hover:underline">Ver Detalhes</a>
                                        </td>
                                    </tr>
                                    <!-- Adicionar mais linhas conforme necessário -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="lista-inspecoes-inspetor-pagination">
                        <nav aria-label="Pagination">
                            <ul class="lista-inspecoes-inspetor-pagination-list">
                                <li class="lista-inspecoes-inspetor-pagination-item">
                                    <a href="#" class="lista-inspecoes-inspetor-pagination-link">
                                        <span class="sr-only">Previous</span>
                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                            fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd"
                                                d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z"
                                                clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                </li>
                                <li class="lista-inspecoes-inspetor-pagination-item">
                                    <a href="#" aria-current="page"
                                        class="lista-inspecoes-inspetor-pagination-link active">1</a>
                                </li>
                                <li class="lista-inspecoes-inspetor-pagination-item">
                                    <a href="#" class="lista-inspecoes-inspetor-pagination-link">2</a>
                                </li>
                                <li class="lista-inspecoes-inspetor-pagination-item">
                                    <a href="#" class="lista-inspecoes-inspetor-pagination-link">
                                        <span class="sr-only">Next</span>
                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                            fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd"
                                                d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
                                                clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function () {            // Mock de dados de inspeção - Em um aplicativo real, isso viria de um backend
            const inspections = [
                {
                    id: "INSP-001",
                    address: "Rua das Palmeiras, 123, Cidade Exemplo",
                    status: "Em Andamento",
                    startDate: "15/03/2024",
                    endDate: "30/03/2024",
                    client: "Edifício Central",
                    type: "Estrutural",
                    engineer: "Carlos Silva",
                    contactName: "Ana Paula",
                    contactEmail: "<EMAIL>",
                    contactPhone: "(11) 98765-4321"
                },
                {
                    id: "INSP-002",
                    address: "Avenida Central, 456, Vila Modelo",
                    status: "Agendada",
                    startDate: "10/06/2025",
                    endDate: "12/06/2025",
                    client: "Shopping Plaza",
                    type: "Elétrica",
                    engineer: "Fernanda Costa",
                    contactName: "Pedro Martins",
                    contactEmail: "<EMAIL>",
                    contactPhone: "(21) 91234-5678"
                },
                {
                    id: "INSP-003",
                    address: "Praça da Matriz, 789, Centro",
                    status: "Concluída",
                    startDate: "15/05/2025",
                    endDate: "17/05/2025",
                    client: "Governo Municipal",
                    type: "Viaduto",
                    engineer: "Roberto Almeida",
                    contactName: "Sofia Pereira",
                    contactEmail: "<EMAIL>",
                    contactPhone: "(31) 99999-8888"
                }
            ];

            const tableBody = document.querySelector('tbody');
            const statusFilter = document.getElementById('statusFilter');
            const startDateFilter = document.getElementById('startDateFilter');
            const endDateFilter = document.getElementById('endDateFilter');
            const searchInput = document.querySelector('input[placeholder="Buscar inspeções..."]');

            function getStatusClass(status) {
                switch (status) {
                    case "Agendada":
                        return 'agendada';
                    case "Em Andamento":
                        return 'andamento';
                    case "Concluída":
                        return 'concluida';
                    case "Cancelada":
                        return 'cancelada';
                    default:
                        return '';
                }
            }

            function renderTable(filteredInspections) {
                tableBody.innerHTML = ''; // Limpa a tabela antes de renderizar
                if (filteredInspections.length === 0) {
                    const row = tableBody.insertRow();
                    const cell = row.insertCell();
                    cell.colSpan = 6; // Número de colunas na tabela
                    cell.className = 'lista-inspecoes-inspetor-empty-state';
                    cell.textContent = 'Nenhuma inspeção encontrada com os filtros aplicados.';
                    return;
                }

                filteredInspections.forEach(inspection => {
                    const row = tableBody.insertRow();

                    row.insertCell().outerHTML = `<td style="width: 150px;">${inspection.id}</td>`;
                    row.insertCell().outerHTML = `<td style="width: 250px;">${inspection.address}</td>`;

                    const statusCell = row.insertCell();
                    statusCell.style.width = '150px';
                    statusCell.innerHTML = `<span class="lista-inspecoes-inspetor-status-badge ${getStatusClass(inspection.status)}">${inspection.status}</span>`;

                    row.insertCell().outerHTML = `<td style="width: 150px;">${inspection.startDate}</td>`;
                    row.insertCell().outerHTML = `<td style="width: 150px;">${inspection.endDate || 'N/A'}</td>`;

                    const actionsCell = row.insertCell();
                    actionsCell.style.width = '150px';
                    let actionsHTML = `<a href="/inspetor/detalhes-inspecao?id=${inspection.id}" class="lista-inspecoes-inspetor-action-link">Ver Detalhes</a>`;
                    if (inspection.status === "Agendada") {
                        actionsHTML += `<a href="/inspetor/iniciar-inspecao?id=${inspection.id}" class="lista-inspecoes-inspetor-action-link success">Iniciar</a>`;
                    } else if (inspection.status === "Em Andamento") {
                        actionsHTML += `<a href="/inspetor/iniciar-inspecao?id=${inspection.id}" class="lista-inspecoes-inspetor-action-link success">Continuar</a>`;
                    }
                    actionsCell.innerHTML = actionsHTML;
                });
            }

            function filterAndRender() {
                const searchTerm = searchInput.value.toLowerCase();
                const statusValue = statusFilter.value;
                const startDateValue = startDateFilter.value;
                const endDateValue = endDateFilter.value;

                const filterStartDateTime = startDateValue ? new Date(startDateValue + "T00:00:00Z").getTime() : null;
                const filterEndDateTime = endDateValue ? new Date(endDateValue + "T23:59:59Z").getTime() : null;

                const filteredInspections = inspections.filter(inspection => {
                    const inspectionName = inspection.client ? inspection.client.toLowerCase() : '';
                    const inspectionAddress = inspection.address.toLowerCase();
                    const inspectionId = inspection.id.toLowerCase();

                    const matchesSearch = searchTerm ?
                        inspectionName.includes(searchTerm) ||
                        inspectionAddress.includes(searchTerm) ||
                        inspectionId.includes(searchTerm) : true;

                    const matchesStatus = statusValue ? inspection.status === statusValue : true;

                    let matchesDate = true;
                    if (filterStartDateTime || filterEndDateTime) {
                        const startParts = inspection.startDate.split('/');
                        const inspectionStartDateTime = startParts.length === 3 ? new Date(Date.UTC(parseInt(startParts[2]), parseInt(startParts[1]) - 1, parseInt(startParts[0]))).getTime() : null;

                        const endParts = inspection.endDate ? inspection.endDate.split('/') : null;
                        const inspectionEndDateTime = endParts && endParts.length === 3 ? new Date(Date.UTC(parseInt(endParts[2]), parseInt(endParts[1]) - 1, parseInt(endParts[0]))).getTime() : null;

                        if (filterStartDateTime && inspectionStartDateTime) {
                            matchesDate = matchesDate && (inspectionStartDateTime >= filterStartDateTime);
                        }
                        if (filterEndDateTime && inspectionEndDateTime) {
                            matchesDate = matchesDate && (inspectionEndDateTime <= filterEndDateTime);
                        } else if (filterEndDateTime && inspection.status !== "Concluída" && inspectionStartDateTime) {
                            // Se a data de término do filtro está definida, mas a inspeção não está concluída (não tem data de término real),
                            // e a data de início da inspeção é posterior à data de término do filtro, não deve corresponder.
                            matchesDate = matchesDate && (inspectionStartDateTime <= filterEndDateTime);
                        } else if (filterEndDateTime && !inspectionEndDateTime && inspection.status === "Concluída") {
                            // Caso uma inspeção concluída não tenha data de término, não deve corresponder se o filtro de data final estiver ativo.
                            matchesDate = false;
                        }
                    }
                    return matchesSearch && matchesStatus && matchesDate;
                });
                renderTable(filteredInspections);
            }

            // Adiciona a opção "Cancelada" ao filtro de status, se ainda não existir
            if (!Array.from(statusFilter.options).find(opt => opt.value === "Cancelada")) {
                const canceladaOption = document.createElement('option');
                canceladaOption.value = "Cancelada";
                canceladaOption.textContent = "Cancelada";
                statusFilter.appendChild(canceladaOption);
            }

            // Event listeners para os filtros e busca
            statusFilter.addEventListener('change', filterAndRender);
            startDateFilter.addEventListener('input', filterAndRender);
            endDateFilter.addEventListener('input', filterAndRender);
            searchInput.addEventListener('input', filterAndRender);

            // Renderiza a tabela inicialmente com todos os dados
            renderTable(inspections);
        });
    </script>
</body>

</html>